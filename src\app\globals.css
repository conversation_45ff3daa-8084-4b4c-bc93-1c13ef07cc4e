@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #cbd5e1;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #94a3b8;
}

/* Dark mode scrollbar */
.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #64748b;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #475569;
}

/* Notification bell animation */
@keyframes notification-ring {
  0% {
    transform: rotate(0deg);
  }
  10% {
    transform: rotate(8deg);
  }
  20% {
    transform: rotate(-6deg);
  }
  30% {
    transform: rotate(4deg);
  }
  40% {
    transform: rotate(-2deg);
  }
  50% {
    transform: rotate(1deg);
  }
  60% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

@keyframes notification-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4);
  }
  50% {
    box-shadow: 0 0 0 6px rgba(239, 68, 68, 0);
  }
}

@keyframes notification-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.notification-bell-active {
  animation: notification-ring 3s ease-in-out infinite;
}

.notification-badge-glow {
  animation: notification-glow 2s ease-in-out infinite;
}

.notification-badge-pulse {
  animation: notification-pulse 1.5s ease-in-out infinite;
}

/* Animação mais intensa para novas notificações */
@keyframes notification-new {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  25% {
    transform: scale(1.1);
    opacity: 0.9;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
  75% {
    transform: scale(1.1);
    opacity: 0.9;
  }
}

.notification-new {
  animation: notification-new 0.8s ease-in-out infinite;
}

@layer base {
  :root {
    /* Background principal - cinza muito claro ao invés de branco puro */
    --background: 220 13% 98%;
    --foreground: 224 71.4% 4.1%;
    
    /* Cards - branco suave com sutil tom de cinza */
    --card: 0 0% 99%;
    --card-foreground: 224 71.4% 4.1%;
    
    /* Popovers - fundo neutro */
    --popover: 0 0% 99%;
    --popover-foreground: 224 71.4% 4.1%;
    
    /* Cores primárias - cinza escuro mais suave */
    --primary: 220 14.3% 25.9%;
    --primary-foreground: 210 20% 98%;
    
    /* Cores secundárias - cinza claro mais refinado */
    --secondary: 220 13% 95%;
    --secondary-foreground: 220 8.9% 46.1%;
    
    /* Cores muted - cinza neutro */
    --muted: 220 13% 96%;
    --muted-foreground: 220 8.9% 46.1%;
    
    /* Cores de destaque - cinza azulado claro */
    --accent: 220 13% 95%;
    --accent-foreground: 220 8.9% 46.1%;
    
    /* Cores destrutivas mantidas */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    
    /* Bordas - cinza mais visível */
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 220 14.3% 25.9%;
    
    /* Raio de bordas */
    --radius: 0.5rem;
    
    /* Cor de aviso */
    --warning: 45 92% 95%;
    --warning-foreground: 45 92% 30%;
    
    /* Cores de gráficos */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    
    /* Cores do tenant personalizáveis */
    --tenant-primary: #1e293b;
    --tenant-primary-light: #475569;
    --tenant-primary-dark: #0f172a;
    --tenant-secondary: #64748b;
    --tenant-secondary-light: #94a3b8;
    --tenant-secondary-dark: #475569;
  }

  .dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 210 20% 98%;
    --primary-foreground: 224 71.4% 4.1%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 216 12.2% 83.9%;
    
    /* Cor de aviso para tema escuro */
    --warning: 45 92% 15%;
    --warning-foreground: 45 92% 85%;
    
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    
    /* Cores do tenant para tema escuro */
    --tenant-primary: var(--tenant-primary-dark);
    --tenant-secondary: var(--tenant-secondary-dark);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    /* Adiciona uma textura sutil ao fundo */
    background-image: radial-gradient(circle at 1px 1px, rgba(0,0,0,0.02) 1px, transparent 0);
    background-size: 20px 20px;
  }
}

@layer components {
  /*
    Reseta a aparência do input de busca para remover o "X" padrão do navegador.
    Isso garante que apenas o nosso botão de limpar customizado seja exibido,
    mantendo a consistência visual em todos os navegadores (especialmente WebKit).
  */
  input[type="search"]::-webkit-search-decoration,
  input[type="search"]::-webkit-search-cancel-button,
  input[type="search"]::-webkit-search-results-button,
  input[type="search"]::-webkit-search-results-decoration {
    -webkit-appearance: none;
    appearance: none;
    display: none;
  }

  /* Classe para ocultar scrollbar mas manter funcionalidade */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}

/* Cores específicas para melhor separação visual */
.layout-surface {
  @apply bg-white/80 backdrop-blur-sm border border-border/50;
}

.content-surface {
  @apply bg-white border border-border shadow-sm;
}

.subtle-surface {
  @apply bg-muted/30 border border-border/30;
}

.tenant-primary {
  color: var(--tenant-primary);
}

.tenant-primary-bg {
  background-color: var(--tenant-primary);
}

.tenant-primary-border {
  border-color: var(--tenant-primary) !important;
}

.tenant-secondary {
  color: var(--tenant-secondary);
}

.tenant-secondary-bg {
  background-color: var(--tenant-secondary);
}

.tenant-secondary-border {
  border-color: var(--tenant-secondary);
}

/* Garantir que as cores do tenant sejam aplicadas independente do tema */
.tenant-primary-border,
.dark .tenant-primary-border {
  border-color: var(--tenant-primary) !important;
}

.tenant-secondary-border,
.dark .tenant-secondary-border {
  border-color: var(--tenant-secondary) !important;
}

/* Utilitários para truncar texto */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  word-break: break-word;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  word-break: break-word;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  word-break: break-word;
}

/* Estilos específicos para o calendário */
.calendar-event-block {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.calendar-event-block:hover {
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.15);
}

.calendar-grid-cell {
  position: relative;
  transition: background-color 0.15s ease;
}

.calendar-grid-cell:hover {
  background-color: rgba(var(--muted), 0.1);
}

/* Utilitários para melhor organização visual */
.page-container {
  @apply bg-gradient-to-br from-background via-neutral-50 to-neutral-100;
}

.card-elevated {
  @apply bg-white border border-border/60 shadow-sm hover:shadow-md transition-all duration-200;
}

.surface-elevated {
  @apply bg-white/95 backdrop-blur-sm border border-border/50 shadow-sm;
}

.divider-subtle {
  @apply border-border/40;
}

.text-subtle {
  @apply text-muted-foreground/80;
}

/* Z-index hierarchy para evitar sobreposições */
.dropdown-overlay {
  @apply z-50;
}

.modal-overlay {
  @apply z-40;
}

.header-fixed {
  @apply z-30;
}

.sidebar-fixed {
  @apply z-20;
}

.card-layer {
  @apply z-10;
}

/* Garantir que cards do dashboard não sobreponham dropdown menus */
.dashboard-stats-card {
  @apply z-auto;
  position: relative;
  isolation: isolate;
}

/* Estilos específicos para a agenda */
@layer components {
  .calendar-grid-cell {
    position: relative;
  }
  
  .calendar-event-block {
    transition: all 0.2s ease-in-out;
  }
  
  .calendar-event-block:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  /* Linha de hora atual com animação */
  .current-time-line {
    position: relative;
    background: linear-gradient(90deg, #ef4444, #dc2626);
    animation: pulse-line 2s ease-in-out infinite;
  }
  
  .current-time-indicator {
    background: #ef4444;
    border: 2px solid #ffffff;
    box-shadow: 0 0 0 2px #ef4444, 0 2px 8px rgba(239, 68, 68, 0.3);
    animation: pulse-indicator 2s ease-in-out infinite;
  }
  
  .current-time-label {
    background: #ef4444;
    color: white;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
    animation: fade-in-out 3s ease-in-out infinite;
  }
  
  @keyframes pulse-line {
    0%, 100% {
      opacity: 1;
      box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4);
    }
    50% {
      opacity: 0.8;
      box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
    }
  }
  
  @keyframes pulse-indicator {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
  }
  
  @keyframes fade-in-out {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.8;
    }
  }
}

/* Animações para importação de dados */
@layer utilities {
  .import-validating {
    @apply bg-blue-50/80 dark:bg-blue-900/20;
    animation: validating-pulse 1.5s ease-in-out infinite;
  }
  
  .import-importing {
    @apply bg-green-50/80 dark:bg-green-900/20;
    animation: importing-pulse 1.2s ease-in-out infinite;
  }
  
  .import-success {
    @apply bg-emerald-50 dark:bg-emerald-900/30;
    animation: success-glow 0.8s ease-out;
  }
  
  @keyframes validating-pulse {
    0%, 100% { 
      background-color: rgb(239 246 255 / 0.8);
      transform: scale(1);
    }
    50% { 
      background-color: rgb(219 234 254 / 0.9);
      transform: scale(1.002);
    }
  }
  
  .dark .import-validating {
    animation: validating-pulse-dark 1.5s ease-in-out infinite;
  }
  
  @keyframes validating-pulse-dark {
    0%, 100% { 
      background-color: rgb(30 58 138 / 0.2);
      transform: scale(1);
    }
    50% { 
      background-color: rgb(30 58 138 / 0.3);
      transform: scale(1.002);
    }
  }
  
  @keyframes importing-pulse {
    0%, 100% { 
      background-color: rgb(240 253 244 / 0.8);
      transform: scale(1);
    }
    50% { 
      background-color: rgb(220 252 231 / 0.9);
      transform: scale(1.002);
    }
  }
  
  .dark .import-importing {
    animation: importing-pulse-dark 1.2s ease-in-out infinite;
  }
  
  @keyframes importing-pulse-dark {
    0%, 100% { 
      background-color: rgb(20 83 45 / 0.2);
      transform: scale(1);
    }
    50% { 
      background-color: rgb(20 83 45 / 0.3);
      transform: scale(1.002);
    }
  }
  
  @keyframes success-glow {
    0% { 
      background-color: rgb(240 253 244);
      box-shadow: 0 0 0 rgba(34 197 94 / 0);
    }
    50% { 
      background-color: rgb(220 252 231);
      box-shadow: 0 0 10px rgba(34 197 94 / 0.3);
    }
    100% { 
      background-color: rgb(236 253 245);
      box-shadow: 0 0 0 rgba(34 197 94 / 0);
    }
  }
  
  .dark .import-success {
    animation: success-glow-dark 0.8s ease-out;
  }
  
  @keyframes success-glow-dark {
    0% {
      background-color: rgb(6 78 59 / 0.3);
      box-shadow: 0 0 0 rgba(34 197 94 / 0);
    }
    50% {
      background-color: rgb(6 78 59 / 0.4);
      box-shadow: 0 0 10px rgba(34 197 94 / 0.2);
    }
    100% {
      background-color: rgb(6 78 59 / 0.3);
      box-shadow: 0 0 0 rgba(34 197 94 / 0);
    }
  }

  /* Animação shimmer para loading states */
  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  .animate-shimmer {
    animation: shimmer 2s infinite;
  }

  /* Melhorias de responsividade para gráficos */
  .chart-responsive {
    @apply w-full h-full min-h-[250px] sm:min-h-[300px] lg:min-h-[350px];
  }

  .chart-container-mobile {
    @apply overflow-x-auto scrollbar-hide;
  }

  /* Utilitários para texto responsivo */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl lg:text-2xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl lg:text-3xl;
  }

  /* Padding responsivo */
  .padding-responsive {
    @apply p-3 sm:p-4 lg:p-6;
  }

  .padding-responsive-x {
    @apply px-3 sm:px-4 lg:px-6;
  }

  .padding-responsive-y {
    @apply py-3 sm:py-4 lg:py-6;
  }

  /* Gap responsivo */
  .gap-responsive {
    @apply gap-3 sm:gap-4 lg:gap-6;
  }

  .gap-responsive-sm {
    @apply gap-2 sm:gap-3 lg:gap-4;
  }

  /* Melhorias para cards em mobile */
  .card-mobile-optimized {
    @apply min-h-[120px] sm:min-h-[140px] lg:min-h-[160px];
  }

  /* Tooltip responsivo */
  .tooltip-responsive {
    @apply max-w-[280px] sm:max-w-none text-xs sm:text-sm;
  }
}
